import { FastifyInstance } from 'fastify';
import { Product } from '../models';

// 请求参数类型定义
interface SearchQuery {
  q: string;
  page?: number;
  limit?: number;
  category?: string;
  platform?: string;
  highlight?: boolean;
}

interface SuggestionQuery {
  q: string;
  limit?: number;
}

// 搜索路由
export async function searchRoutes(fastify: FastifyInstance) {
  // 全文搜索产品
  fastify.get<{ Querystring: SearchQuery & { lang?: string } }>('/search', async (request, reply) => {
    try {
      const {
        q,
        page = 1,
        limit = 20,
        category,
        platform,
        highlight = false,
        lang = 'zh'
      } = request.query;
      
      if (!q || q.trim().length === 0) {
        return reply.status(400).send({
          success: false,
          error: {
            code: 'EMPTY_SEARCH_QUERY',
            message: '搜索关键词不能为空'
          },
          timestamp: new Date().toISOString()
        });
      }
      
      // 根据语言选择对应的字段路径
      const getFieldPath = (baseField: string) => {
        if (lang === 'en') {
          return `${baseField}.english`;
        }
        return `${baseField}.chinese`;
      };

      // 构建基础查询条件
      const baseQuery = {
        status: 'active',
        isVisible: true
      };

      // 构建文本搜索查询
      const textSearchQuery = {
        $text: { $search: q },
        ...baseQuery
      };

      // 构建正则表达式搜索查询
      const searchRegex = new RegExp(q.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
      const regexSearchQuery = {
        $or: [
          { 'name.display': searchRegex },
          { 'name.english': searchRegex },
          { 'name.chinese': searchRegex },
          { 'category.primary.display': searchRegex },
          { 'category.secondary.display': searchRegex },
          { 'platform.display': searchRegex },
          { 'manufacturer.display': searchRegex },
          { 'specification.display': searchRegex },
          { 'flavor.display': searchRegex }
        ],
        ...baseQuery
      };

      // 应用额外的筛选条件
      const applyFilters = (query: any) => {
        if (category) {
          query.$and = query.$and || [];
          query.$and.push({
            $or: [
              { [getFieldPath('category.primary')]: category },
              { 'category.primary.display': category }
            ]
          });
        }
        if (platform) {
          query.$and = query.$and || [];
          query.$and.push({
            $or: [
              { [getFieldPath('platform')]: platform },
              { 'platform.display': platform }
            ]
          });
        }
        return query;
      };

      // 应用筛选条件到两个查询
      const finalTextSearchQuery = applyFilters({ ...textSearchQuery });
      const finalRegexSearchQuery = applyFilters({ ...regexSearchQuery });

      const maxLimit = 100;
      const actualLimit = Math.min(limit, maxLimit);

      // 计算候选集大小：略大于请求的limit以确保有足够数据进行高质量排序
      const candidateLimit = Math.max(actualLimit * 2, 50);

      // 并行执行文本搜索和正则搜索
      const [textSearchResults, regexSearchResults] = await Promise.all([
        // 文本搜索
        Product.find(finalTextSearchQuery, { score: { $meta: 'textScore' } })
          .sort({ score: { $meta: 'textScore' }, collectTime: -1 })
          .limit(candidateLimit)
          .lean()
          .catch(() => []), // 如果文本搜索失败，返回空数组

        // 正则搜索
        Product.find(finalRegexSearchQuery)
          .sort({ collectTime: -1 })
          .limit(candidateLimit)
          .lean()
          .catch(() => []) // 如果正则搜索失败，返回空数组
      ]);

      // 合并和去重结果
      const mergedResults = new Map();

      // 首先添加文本搜索结果（优先保留，因为有textScore）
      textSearchResults.forEach((product: any) => {
        mergedResults.set(product.productId, {
          ...product,
          searchType: 'text',
          textScore: product.score || 0
        });
      });

      // 然后添加正则搜索结果（如果productId不存在的话）
      regexSearchResults.forEach((product: any) => {
        if (!mergedResults.has(product.productId)) {
          mergedResults.set(product.productId, {
            ...product,
            searchType: 'regex',
            textScore: 0 // 正则搜索没有textScore，设为0
          });
        }
      });

      // 转换为数组并进行基础排序（后续可以在这里添加综合评分）
      const allResults = Array.from(mergedResults.values()).sort((a, b) => {
        // 优先按textScore排序，然后按collectTime排序
        if (a.textScore !== b.textScore) {
          return b.textScore - a.textScore;
        }
        return new Date(b.collectTime).getTime() - new Date(a.collectTime).getTime();
      });

      // 计算总数和分页
      const total = allResults.length;
      const skip = (page - 1) * actualLimit;
      const products = allResults.slice(skip, skip + actualLimit);

      // 如果需要高亮，处理搜索结果
      let processedProducts = products;
      if (highlight) {
        processedProducts = products.map(product => ({
          ...product,
          highlightedName: highlightText(product.name?.display || '', q),
          highlightedManufacturer: product.manufacturer ? highlightText(product.manufacturer.display || '', q) : undefined
        }));
      }

      const totalPages = Math.ceil(total / actualLimit);
      
      return {
        success: true,
        data: {
          products: processedProducts,
          searchQuery: q,
          pagination: {
            page: parseInt(page.toString()),
            limit: actualLimit,
            total,
            totalPages,
            hasNext: skip + actualLimit < total,
            hasPrev: page > 1
          }
        },
        message: `找到 ${total} 个相关产品`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      fastify.log.error('搜索产品失败:', error);
      return reply.status(500).send({
        success: false,
        error: {
          code: 'SEARCH_ERROR',
          message: '搜索产品失败'
        },
        timestamp: new Date().toISOString()
      });
    }
  });
  
  // 搜索建议/自动补全
  fastify.get<{ Querystring: SuggestionQuery }>('/search/suggestions', async (request, reply) => {
    try {
      const { q, limit = 10 } = request.query;
      
      if (!q || q.trim().length < 2) {
        return {
          success: true,
          data: {
            suggestions: []
          },
          message: '搜索关键词太短',
          timestamp: new Date().toISOString()
        };
      }
      
      const maxLimit = 20;
      const actualLimit = Math.min(limit, maxLimit);
      
      // 使用正则表达式进行模糊匹配
      const regex = new RegExp(q.trim(), 'i');
      
      // 搜索产品名称和制造商
      const suggestions = await Product.aggregate([
        {
          $match: {
            $or: [
              { name: { $regex: regex } },
              { manufacturer: { $regex: regex } }
            ],
            status: 'active',
            isVisible: true
          }
        },
        {
          $group: {
            _id: null,
            names: { $addToSet: '$name' },
            manufacturers: { $addToSet: '$manufacturer' }
          }
        },
        {
          $project: {
            suggestions: {
              $slice: [
                {
                  $filter: {
                    input: { $concatArrays: ['$names', '$manufacturers'] },
                    cond: { 
                      $regexMatch: { 
                        input: '$$this', 
                        regex: q.trim(), 
                        options: 'i' 
                      } 
                    }
                  }
                },
                actualLimit
              ]
            }
          }
        }
      ]);
      
      const result = suggestions.length > 0 ? suggestions[0].suggestions : [];
      
      return {
        success: true,
        data: {
          suggestions: result,
          query: q
        },
        message: `找到 ${result.length} 个建议`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      fastify.log.error('获取搜索建议失败:', error);
      return reply.status(500).send({
        success: false,
        error: {
          code: 'SUGGESTION_ERROR',
          message: '获取搜索建议失败'
        },
        timestamp: new Date().toISOString()
      });
    }
  });
}

// 高亮文本函数
function highlightText(text: string, query: string): string {
  if (!text || !query) return text;
  
  const words = query.trim().split(/\s+/);
  let highlightedText = text;
  
  words.forEach(word => {
    const regex = new RegExp(`(${word})`, 'gi');
    highlightedText = highlightedText.replace(regex, '<mark>$1</mark>');
  });
  
  return highlightedText;
}