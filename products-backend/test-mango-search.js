const mongoose = require('mongoose');
require('dotenv').config();

async function detailedMangoTest() {
  await mongoose.connect(process.env.MONGODB_URI);
  const db = mongoose.connection.db;
  const collection = db.collection('products');
  
  const query = '芒果';
  console.log('=== 详细芒果搜索测试 ===');
  
  // 构建基础查询条件
  const baseQuery = {
    status: 'active',
    isVisible: true
  };

  // 构建文本搜索查询
  const textSearchQuery = {
    $text: { $search: query },
    ...baseQuery
  };

  // 构建正则表达式搜索查询
  const searchRegex = new RegExp(query.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
  const regexSearchQuery = {
    $or: [
      { 'name.display': searchRegex },
      { 'name.english': searchRegex },
      { 'name.chinese': searchRegex },
      { 'category.primary.display': searchRegex },
      { 'category.secondary.display': searchRegex },
      { 'platform.display': searchRegex },
      { 'manufacturer.display': searchRegex },
      { 'specification.display': searchRegex },
      { 'flavor.display': searchRegex }
    ],
    ...baseQuery
  };

  // 并行执行搜索
  const [textResults, regexResults] = await Promise.all([
    collection.find(textSearchQuery, { score: { $meta: 'textScore' } })
      .sort({ score: { $meta: 'textScore' }, collectTime: -1 })
      .limit(50)
      .toArray()
      .catch(() => []),
    
    collection.find(regexSearchQuery)
      .sort({ collectTime: -1 })
      .limit(50)
      .toArray()
      .catch(() => [])
  ]);

  console.log('文本搜索结果数量:', textResults.length);
  textResults.forEach((product, index) => {
    console.log(`  ${index + 1}. ${product.name?.display || '未知产品'} (评分: ${product.score?.toFixed(2) || 'N/A'})`);
  });
  
  console.log('\n正则搜索结果数量:', regexResults.length);
  regexResults.slice(0, 5).forEach((product, index) => {
    console.log(`  ${index + 1}. ${product.name?.display || '未知产品'}`);
  });
  
  // 合并去重
  const mergedResults = new Map();
  
  textResults.forEach((product) => {
    mergedResults.set(product.productId, {
      ...product,
      searchType: 'text',
      textScore: product.score || 0
    });
  });
  
  regexResults.forEach((product) => {
    if (!mergedResults.has(product.productId)) {
      mergedResults.set(product.productId, {
        ...product,
        searchType: 'regex',
        textScore: 0
      });
    }
  });
  
  const finalResults = Array.from(mergedResults.values()).sort((a, b) => {
    if (a.textScore !== b.textScore) {
      return b.textScore - a.textScore;
    }
    return new Date(b.collectTime).getTime() - new Date(a.collectTime).getTime();
  });
  
  console.log('\n合并后最终结果数量:', finalResults.length);
  console.log('前5个结果:');
  finalResults.slice(0, 5).forEach((product, index) => {
    console.log(`  ${index + 1}. ${product.name?.display || '未知产品'} (类型: ${product.searchType}, 评分: ${product.textScore?.toFixed(2) || 'N/A'})`);
  });
  
  await mongoose.disconnect();
}

detailedMangoTest().catch(console.error);
