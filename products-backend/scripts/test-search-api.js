#!/usr/bin/env node

/**
 * 直接测试搜索API逻辑
 */

const mongoose = require('mongoose');
const { config } = require('dotenv');

// 加载环境变量
config();

// 导入Product模型
const { Product } = require('../src/models');

async function testSearchLogic() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: false,
      w: 1,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    
    console.log('数据库连接成功');
    
    const q = '卡士';
    console.log(`测试搜索关键词: "${q}"`);
    
    // 模拟搜索路由逻辑
    let query;
    let sortCondition;
    
    // 首先尝试文本搜索
    try {
      const textSearchQuery = {
        $text: { $search: q },
        status: 'active',
        isVisible: true
      };
      
      console.log('尝试文本搜索...');
      const textSearchCount = await Product.countDocuments(textSearchQuery);
      console.log(`文本搜索结果数量: ${textSearchCount}`);
      
      if (textSearchCount > 0) {
        query = textSearchQuery;
        sortCondition = { 
          score: { $meta: 'textScore' }, 
          collectTime: -1 
        };
        console.log('使用文本搜索');
      } else {
        throw new Error('No text search results, fallback to regex');
      }
    } catch (error) {
      console.log('文本搜索失败，使用正则表达式搜索');
      
      const searchRegex = new RegExp(q.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
      
      query = {
        $or: [
          { 'name.display': searchRegex },
          { 'name.english': searchRegex },
          { 'name.chinese': searchRegex },
          { 'category.primary.display': searchRegex },
          { 'category.secondary.display': searchRegex },
          { 'platform.display': searchRegex },
          { 'manufacturer.display': searchRegex },
          { 'specification.display': searchRegex },
          { 'flavor.display': searchRegex }
        ],
        status: 'active',
        isVisible: true
      };
      
      sortCondition = { collectTime: -1 };
    }
    
    console.log('执行搜索查询...');
    console.log('查询条件:', JSON.stringify(query, null, 2));
    
    const products = await Product.find(query)
      .sort(sortCondition)
      .limit(5)
      .lean();
    
    console.log(`搜索结果数量: ${products.length}`);
    
    products.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name?.display || '未知产品'}`);
      console.log(`   制造商: ${product.manufacturer?.display || 'N/A'}`);
      console.log(`   平台: ${product.platform?.display || 'N/A'}`);
    });
    
    await mongoose.disconnect();
    console.log('测试完成');
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testSearchLogic();
