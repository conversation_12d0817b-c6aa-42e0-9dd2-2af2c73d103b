#!/usr/bin/env node

/**
 * 搜索索引迁移脚本
 * 
 * 功能：
 * 1. 删除过时的 searchText 文本索引
 * 2. 创建新的多语言文本搜索索引
 * 3. 支持中英文混合搜索
 * 
 * 使用方法：
 * node scripts/migrate-search-indexes.js [--dry-run] [--verbose]
 */

const mongoose = require('mongoose');
const { config } = require('dotenv');

// 加载环境变量
config();

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  dryRun: args.includes('--dry-run'),
  verbose: args.includes('--verbose'),
  force: args.includes('--force')
};

// 日志函数
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📝',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    debug: '🔍'
  }[level] || '📝';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function debug(message) {
  if (options.verbose) {
    log(message, 'debug');
  }
}

// 新的文本搜索索引配置
const NEW_TEXT_INDEX = {
  key: {
    'name.display': 'text',
    'name.english': 'text',
    'name.chinese': 'text',
    'category.primary.display': 'text',
    'category.secondary.display': 'text',
    'platform.display': 'text',
    'manufacturer.display': 'text',
    'specification.display': 'text',
    'flavor.display': 'text'
  },
  options: {
    name: 'product_text_search',
    weights: {
      'name.display': 10,
      'name.english': 8,
      'name.chinese': 8,
      'category.primary.display': 5,
      'category.secondary.display': 3,
      'platform.display': 3,
      'manufacturer.display': 4,
      'specification.display': 2,
      'flavor.display': 2
    },
    default_language: 'none', // 支持中英文混合，不进行词干提取
    background: true
  }
};

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: false,
      w: 1,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    log('数据库连接成功', 'success');
  } catch (error) {
    log(`数据库连接失败: ${error.message}`, 'error');
    throw error;
  }
}

/**
 * 获取当前索引列表
 */
async function getCurrentIndexes() {
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('products');
    const indexes = await collection.indexes();
    
    debug(`当前索引数量: ${indexes.length}`);
    if (options.verbose) {
      indexes.forEach((index, i) => {
        debug(`索引 ${i + 1}: ${index.name} - ${JSON.stringify(index.key)}`);
      });
    }
    
    return indexes;
  } catch (error) {
    log(`获取索引列表失败: ${error.message}`, 'error');
    throw error;
  }
}

/**
 * 查找并删除旧的文本索引
 */
async function removeOldTextIndexes(indexes) {
  const db = mongoose.connection.db;
  const collection = db.collection('products');
  
  // 查找所有文本索引
  const textIndexes = indexes.filter(index => 
    Object.values(index.key || {}).includes('text')
  );
  
  log(`找到 ${textIndexes.length} 个文本索引`, 'info');
  
  for (const index of textIndexes) {
    try {
      log(`准备删除索引: ${index.name}`, 'warning');
      
      if (!options.dryRun) {
        await collection.dropIndex(index.name);
        log(`成功删除索引: ${index.name}`, 'success');
      } else {
        log(`[DRY RUN] 将删除索引: ${index.name}`, 'info');
      }
    } catch (error) {
      if (error.code === 27 || error.message.includes('index not found')) {
        log(`索引 ${index.name} 不存在，跳过删除`, 'warning');
      } else {
        log(`删除索引 ${index.name} 失败: ${error.message}`, 'error');
        throw error;
      }
    }
  }
  
  return textIndexes.length;
}

/**
 * 创建新的文本搜索索引
 */
async function createNewTextIndex() {
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('products');
    
    log('准备创建新的文本搜索索引...', 'info');
    debug(`索引配置: ${JSON.stringify(NEW_TEXT_INDEX, null, 2)}`);
    
    if (!options.dryRun) {
      await collection.createIndex(NEW_TEXT_INDEX.key, NEW_TEXT_INDEX.options);
      log('新文本搜索索引创建成功', 'success');
    } else {
      log('[DRY RUN] 将创建新的文本搜索索引', 'info');
    }
    
    return true;
  } catch (error) {
    log(`创建新索引失败: ${error.message}`, 'error');
    throw error;
  }
}

/**
 * 验证索引创建结果
 */
async function validateIndexes() {
  try {
    const indexes = await getCurrentIndexes();
    const textIndexes = indexes.filter(index => 
      Object.values(index.key || {}).includes('text')
    );
    
    if (textIndexes.length === 1 && textIndexes[0].name === 'product_text_search') {
      log('索引验证成功：新的文本搜索索引已正确创建', 'success');
      return true;
    } else {
      log(`索引验证失败：期望1个文本索引，实际找到${textIndexes.length}个`, 'error');
      return false;
    }
  } catch (error) {
    log(`索引验证失败: ${error.message}`, 'error');
    return false;
  }
}

/**
 * 测试搜索功能
 */
async function testSearchFunctionality() {
  try {
    const db = mongoose.connection.db;
    const collection = db.collection('products');
    
    log('测试搜索功能...', 'info');
    
    // 测试中文搜索
    const chineseResults = await collection.find({
      $text: { $search: '酸奶' },
      status: 'active',
      isVisible: true
    }).limit(3).toArray();
    
    // 测试英文搜索
    const englishResults = await collection.find({
      $text: { $search: 'yogurt' },
      status: 'active',
      isVisible: true
    }).limit(3).toArray();
    
    log(`中文搜索 '酸奶' 结果数量: ${chineseResults.length}`, 'info');
    log(`英文搜索 'yogurt' 结果数量: ${englishResults.length}`, 'info');
    
    if (chineseResults.length > 0 || englishResults.length > 0) {
      log('搜索功能测试通过', 'success');
      return true;
    } else {
      log('搜索功能测试失败：没有找到任何结果', 'warning');
      return false;
    }
  } catch (error) {
    log(`搜索功能测试失败: ${error.message}`, 'error');
    return false;
  }
}

/**
 * 主执行函数
 */
async function main() {
  try {
    log('开始搜索索引迁移...', 'info');
    
    if (options.dryRun) {
      log('运行模式: DRY RUN (不会实际修改数据库)', 'warning');
    }
    
    // 连接数据库
    await connectDatabase();
    
    // 获取当前索引
    const currentIndexes = await getCurrentIndexes();
    
    // 删除旧的文本索引
    const removedCount = await removeOldTextIndexes(currentIndexes);
    
    // 创建新的文本索引
    await createNewTextIndex();
    
    if (!options.dryRun) {
      // 验证索引创建结果
      const validationResult = await validateIndexes();
      
      if (validationResult) {
        // 测试搜索功能
        await testSearchFunctionality();
      }
    }
    
    log('搜索索引迁移完成', 'success');
    log(`删除了 ${removedCount} 个旧索引，创建了 1 个新索引`, 'info');
    
  } catch (error) {
    log(`迁移失败: ${error.message}`, 'error');
    console.error('详细错误信息:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    log('数据库连接已关闭', 'info');
  }
}

// 执行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  connectDatabase,
  getCurrentIndexes,
  removeOldTextIndexes,
  createNewTextIndex,
  validateIndexes,
  testSearchFunctionality
};
