#!/usr/bin/env node

/**
 * 搜索功能测试脚本
 * 
 * 功能：
 * 1. 测试中英文关键词搜索
 * 2. 验证搜索结果的相关性排序
 * 3. 测试搜索性能
 * 4. 验证不同字段的搜索权重
 * 
 * 使用方法：
 * node scripts/test-search-functionality.js [--verbose]
 */

const mongoose = require('mongoose');
const { config } = require('dotenv');

// 加载环境变量
config();

// 命令行参数解析
const args = process.argv.slice(2);
const options = {
  verbose: args.includes('--verbose')
};

// 日志函数
function log(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📝',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    debug: '🔍',
    test: '🧪'
  }[level] || '📝';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function debug(message) {
  if (options.verbose) {
    log(message, 'debug');
  }
}

// 测试用例
const TEST_CASES = [
  {
    name: '中文产品名称搜索',
    query: '酸奶',
    expectedMinResults: 1,
    description: '搜索包含"酸奶"的产品'
  },
  {
    name: '英文产品名称搜索',
    query: 'yogurt',
    expectedMinResults: 1,
    description: '搜索包含"yogurt"的产品'
  },
  {
    name: '中文品牌搜索',
    query: '卡士',
    expectedMinResults: 1,
    description: '搜索"卡士"品牌产品'
  },
  {
    name: '英文品牌搜索',
    query: 'Cowala',
    expectedMinResults: 1,
    description: '搜索"Cowala"品牌产品'
  },
  {
    name: '分类搜索',
    query: '饮料',
    expectedMinResults: 1,
    description: '搜索饮料类产品'
  },
  {
    name: '平台搜索',
    query: '天猫',
    expectedMinResults: 1,
    description: '搜索天猫平台产品'
  },
  {
    name: '规格搜索',
    query: '440g',
    expectedMinResults: 1,
    description: '搜索440g规格产品'
  },
  {
    name: '混合关键词搜索',
    query: '酸奶 yogurt',
    expectedMinResults: 1,
    description: '搜索包含中英文混合关键词的产品'
  },
  {
    name: '部分匹配搜索',
    query: '益生菌',
    expectedMinResults: 1,
    description: '搜索包含"益生菌"的产品'
  },
  {
    name: '芒果搜索测试',
    query: '芒果',
    expectedMinResults: 1,
    description: '测试"芒果"搜索是否能找到相关产品（如100%芒果复合果汁）'
  },
  {
    name: '空搜索测试',
    query: '',
    expectedMinResults: 0,
    description: '测试空查询的处理',
    shouldFail: true
  }
];

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      retryWrites: false,
      w: 1,
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    });
    log('数据库连接成功', 'success');
  } catch (error) {
    log(`数据库连接失败: ${error.message}`, 'error');
    throw error;
  }
}

/**
 * 执行搜索查询 - 使用与API相同的混合并行搜索策略
 */
async function executeSearch(query, limit = 10) {
  const db = mongoose.connection.db;
  const collection = db.collection('products');

  const startTime = Date.now();

  try {
    // 构建基础查询条件
    const baseQuery = {
      status: 'active',
      isVisible: true
    };

    // 构建文本搜索查询
    const textSearchQuery = {
      $text: { $search: query },
      ...baseQuery
    };

    // 构建正则表达式搜索查询
    const searchRegex = new RegExp(query.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'i');
    const regexSearchQuery = {
      $or: [
        { 'name.display': searchRegex },
        { 'name.english': searchRegex },
        { 'name.chinese': searchRegex },
        { 'category.primary.display': searchRegex },
        { 'category.secondary.display': searchRegex },
        { 'platform.display': searchRegex },
        { 'manufacturer.display': searchRegex },
        { 'specification.display': searchRegex },
        { 'flavor.display': searchRegex }
      ],
      ...baseQuery
    };

    // 计算候选集大小
    const candidateLimit = Math.max(limit * 2, 50);

    // 并行执行文本搜索和正则搜索
    const [textSearchResults, regexSearchResults] = await Promise.all([
      // 文本搜索
      collection.find(textSearchQuery, { score: { $meta: 'textScore' } })
        .sort({ score: { $meta: 'textScore' }, collectTime: -1 })
        .limit(candidateLimit)
        .toArray()
        .catch(() => []), // 如果文本搜索失败，返回空数组

      // 正则搜索
      collection.find(regexSearchQuery)
        .sort({ collectTime: -1 })
        .limit(candidateLimit)
        .toArray()
        .catch(() => []) // 如果正则搜索失败，返回空数组
    ]);

    // 合并和去重结果
    const mergedResults = new Map();

    // 首先添加文本搜索结果（优先保留，因为有textScore）
    textSearchResults.forEach((product) => {
      mergedResults.set(product.productId, {
        ...product,
        searchType: 'text',
        textScore: product.score || 0
      });
    });

    // 然后添加正则搜索结果（如果productId不存在的话）
    regexSearchResults.forEach((product) => {
      if (!mergedResults.has(product.productId)) {
        mergedResults.set(product.productId, {
          ...product,
          searchType: 'regex',
          textScore: 0 // 正则搜索没有textScore，设为0
        });
      }
    });

    // 转换为数组并进行基础排序
    const allResults = Array.from(mergedResults.values()).sort((a, b) => {
      // 优先按textScore排序，然后按collectTime排序
      if (a.textScore !== b.textScore) {
        return b.textScore - a.textScore;
      }
      return new Date(b.collectTime).getTime() - new Date(a.collectTime).getTime();
    });

    // 应用最终的limit
    const results = allResults.slice(0, limit);

    const endTime = Date.now();
    const duration = endTime - startTime;

    return {
      results,
      count: results.length,
      totalCandidates: allResults.length,
      textResults: textSearchResults.length,
      regexResults: regexSearchResults.length,
      duration,
      success: true
    };
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;

    return {
      results: [],
      count: 0,
      duration,
      success: false,
      error: error.message
    };
  }
}

/**
 * 执行单个测试用例
 */
async function runTestCase(testCase) {
  log(`执行测试: ${testCase.name}`, 'test');
  debug(`查询: "${testCase.query}"`);
  debug(`描述: ${testCase.description}`);
  
  if (testCase.shouldFail && !testCase.query.trim()) {
    log(`跳过空查询测试（由API层处理）`, 'warning');
    return { passed: true, skipped: true };
  }
  
  const searchResult = await executeSearch(testCase.query);
  
  debug(`搜索耗时: ${searchResult.duration}ms`);
  debug(`结果数量: ${searchResult.count}`);
  if (searchResult.totalCandidates !== undefined) {
    debug(`候选结果总数: ${searchResult.totalCandidates} (文本: ${searchResult.textResults}, 正则: ${searchResult.regexResults})`);
  }

  if (!searchResult.success) {
    log(`搜索执行失败: ${searchResult.error}`, 'error');
    return { passed: false, error: searchResult.error };
  }

  // 检查结果数量
  const passed = searchResult.count >= testCase.expectedMinResults;

  if (passed) {
    log(`✓ 测试通过 - 找到 ${searchResult.count} 个结果`, 'success');
  } else {
    log(`✗ 测试失败 - 期望至少 ${testCase.expectedMinResults} 个结果，实际 ${searchResult.count} 个`, 'error');
  }
  
  // 显示前几个结果的详情
  if (options.verbose && searchResult.results.length > 0) {
    debug('搜索结果详情:');
    searchResult.results.slice(0, 3).forEach((product, index) => {
      debug(`  ${index + 1}. ${product.name?.display || '未知产品'} (评分: ${product.score?.toFixed(2) || 'N/A'})`);
      if (product.manufacturer?.display) {
        debug(`     制造商: ${product.manufacturer.display}`);
      }
      if (product.category?.primary?.display) {
        debug(`     分类: ${product.category.primary.display}`);
      }
    });
  }
  
  return {
    passed,
    count: searchResult.count,
    duration: searchResult.duration,
    results: searchResult.results
  };
}

/**
 * 性能测试
 */
async function performanceTest() {
  log('执行性能测试...', 'test');
  
  const testQueries = ['酸奶', 'yogurt', '卡士', '饮料', '天猫'];
  const iterations = 5;
  const results = [];
  
  for (const query of testQueries) {
    const durations = [];
    
    for (let i = 0; i < iterations; i++) {
      const result = await executeSearch(query);
      durations.push(result.duration);
    }
    
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    const minDuration = Math.min(...durations);
    
    results.push({
      query,
      avgDuration: Math.round(avgDuration),
      maxDuration,
      minDuration
    });
    
    debug(`查询 "${query}" - 平均: ${avgDuration.toFixed(1)}ms, 最大: ${maxDuration}ms, 最小: ${minDuration}ms`);
  }
  
  const overallAvg = results.reduce((sum, r) => sum + r.avgDuration, 0) / results.length;
  
  if (overallAvg < 100) {
    log(`性能测试通过 - 平均响应时间: ${overallAvg.toFixed(1)}ms`, 'success');
  } else if (overallAvg < 500) {
    log(`性能测试警告 - 平均响应时间: ${overallAvg.toFixed(1)}ms (建议优化)`, 'warning');
  } else {
    log(`性能测试失败 - 平均响应时间: ${overallAvg.toFixed(1)}ms (需要优化)`, 'error');
  }
  
  return results;
}

/**
 * 主测试函数
 */
async function main() {
  try {
    log('开始搜索功能测试...', 'info');
    
    // 连接数据库
    await connectDatabase();
    
    // 执行功能测试
    let passedTests = 0;
    let totalTests = 0;
    let skippedTests = 0;
    
    for (const testCase of TEST_CASES) {
      const result = await runTestCase(testCase);
      
      if (result.skipped) {
        skippedTests++;
      } else {
        totalTests++;
        if (result.passed) {
          passedTests++;
        }
      }
      
      // 测试间隔
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 执行性能测试
    const performanceResults = await performanceTest();
    
    // 输出测试总结
    log('=== 测试总结 ===', 'info');
    log(`功能测试: ${passedTests}/${totalTests} 通过 (跳过: ${skippedTests})`, 'info');
    
    if (passedTests === totalTests) {
      log('所有功能测试通过！', 'success');
    } else {
      log(`${totalTests - passedTests} 个测试失败`, 'error');
    }
    
    log(`平均搜索响应时间: ${performanceResults.reduce((sum, r) => sum + r.avgDuration, 0) / performanceResults.length}ms`, 'info');
    
    const success = passedTests === totalTests;
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    log(`测试执行失败: ${error.message}`, 'error');
    console.error('详细错误信息:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    log('数据库连接已关闭', 'info');
  }
}

// 执行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('未捕获的错误:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  executeSearch,
  runTestCase,
  performanceTest
};
